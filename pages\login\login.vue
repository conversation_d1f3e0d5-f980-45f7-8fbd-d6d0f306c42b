<template>
	<view class="appPage">
		<image class="bg" src="../../static/images/index/bg.png" mode=""></image>
		<view class="content" v-if="otherLogin || !oneKeyLoginFlag">
			<view class="t_betweent">
				<view class="inputBg areaCode"> +86 </view>
				<view class="inputBg inputRadio">
					<u-input v-model="username" type="number" maxlength="11" placeholder="请输入手机号" :border="false"
						:custom-style="{ fontSize: '38rpx' }" clearable :show-confirmbar="false" style="width: 100%" />
				</view>
			</view>

			<view class="uni-margin-wrap" style="height: 120rpx">
				<swiper class="swiper" :current="swiperIndex" @change="setCurrent">
					<swiper-item>
						<view class="inputBg" style="padding: 26rpx">
							<u-input v-model="code" type="number" inputmode="numeric" pattern="[0-9]*" maxlength="6"
								placeholder="请输入验证码" :border="false" clearable :custom-style="{ fontSize: '32rpx' }"
								:show-confirmbar="false" />
							<view @click="getCheckNum" class="codoColor">{{ !codeTime ? "获取验证码" : codeTime + "秒重新获取" }}
							</view>
						</view>
					</swiper-item>
					<swiper-item>
						<view class="inputBg" style="padding: 26rpx">
							<u-input v-model="inputValue" type="text" placeholder="请输入密码" :border="false"
								:custom-style="{ fontSize: '32rpx' }" confirm-hold :show-confirmbar="false"
								@input="handlePasswordInput" @confirm="toPwd" style="flex: 1" />
							<view @click="togglePasswordVisibility" style="padding-left: 10rpx">
								<u-icon :name="showPassword ? 'eye' : 'eye-off'" size="40"></u-icon>
							</view>
						</view>
					</swiper-item>
				</swiper>
			</view>
			<view class="btn t_betweent">
				<view class=""></view>
				<span class="forgotPassword" @click="goNav('/pages/findPwd/findPwd')"
					style="margin-right: 40rpx; z-index: 99">忘记密码</span>
			</view>
			<view class="check">
				<radio :checked="checked" @click="checked = !checked" style="transform: scale(0.7); margin-top: -1px" />
				<span style="line-height: 45rpx">我已阅读并同意<span class="xy"
						@click="goNav('/pages/privacyAgreement/userAgreement')">《用户服务协议》</span>和<span class="xy"
						@click="goNav('/pages/privacyAgreement/privacyAgreement')">《隐私政策》</span></span>
			</view>
			<view class="login" :style="{ background: flag ? '' : '#767676' }" @click="goLogin">
				{{ btnName }}
			</view>
			<view class="bottomBtn">
				<view class="t_center" @click="swiperIndex = !swiperIndex + 0">
					<image class="img76" src="../../static/login/pwd.png" mode=""></image>
					<view class="" style="margin-top: 12rpx">
						{{ swiperArr[swiperIndex] }}
					</view>
				</view>
			</view>
			<view class="baStype">ICP备案/许可证号:黑ICP备2023013144号-1A</view>
		</view>

		<view class="content" v-else>
			<view class="login-wrap-new uni-margin-wrap" style="height: 120rpx">
				<view class="phone-wrap" v-if="oneKeyLoginFlag">
					<view class="phone-text" v-if="maskPhone">{{ maskPhone }}</view>
					<button class="main-login-btn" @click="oneKeyLogin">
						本机号码一键登录/注册
					</button>
				</view>
				<view class="login-types">
					<view class="check" style="margin-bottom: 20rpx">
						<radio :checked="checked" @click="checked = !checked"
							style="transform: scale(0.7); margin-top: -1px" />
						<span style="line-height: 45rpx">我已阅读并同意<span class="xy"
								@click="goNav('/pages/privacyAgreement/userAgreement')">《用户服务协议》</span>和<span class="xy"
								@click="goNav('/pages/privacyAgreement/privacyAgreement')">《隐私政策》</span></span>
					</view>
					<view class="other-login-row">
						<view class="other-login-btn phone" @click="otherPhoneLogin">
							<image src="@/static/login/icon_phone.png" class="icon"></image>
						</view>
						<!-- <view class="other-login-btn wechat" @click="wechatLogin">
							<image src="@/static/login/icon_wechat.png" class="icon"></image>
						</view> -->
					</view>
					<view class="other-login-tip">选择一种其他登录方式登录</view>
				</view>
			</view>
		</view>
		<uni-popup ref="locationPopup" :isMaskClick="false">
			<view class="location-content">
				<view class="loca-name">欢迎登陆</view>
				<view class="loca-tip">请允许lightingball使用定位权限</view>
				<view class="loca-txt">为了提供区更贴近您兴趣的本地资讯及您所在位区域的信息服务，我们需要获取您设备的所在区域信息。不授权不影响您使用APP</view>
				<view class="loca-ty" @click="locaTy">同意</view>
				<view class="loca-quit" @click="easyLogin">不同意</view>
			</view>
		</uni-popup>
		<u-toast ref="notify" />
	</view>
</template>

<script>
	import {
		config
	} from "@/config.js";
	const BASE_URL = config.BASE_URL_App;
	import {
		apiLocationReport
	} from "@/api/common.js";
	import {
		wgs84_to_gcj02
	} from "../index/wgs84_to_gcj02";
	export default {
		data() {
			return {
				btnName: "",
				checked: false,
				showClearIcon: true,
				username: "",
				realPwd: "", // 存储实际密码
				code: "",
				swiperIndex: 0,
				codeTime: 0,
				swiperArr: ["密码登录", "验证码登录"],
				showPassword: false,
				inputValue: "",
				otherLogin: false,
				maskPhone: "",
				token: "",
				oneKeyLoginFlag: false,
			};
		},

		onLoad() {
			// 本机号码一键登录/注册逻辑
			uni.preLogin({
				provider: 'univerify',
				success: (res) => { //预登录成功
					this.oneKeyLoginFlag = true
				},
				fail: (res) => {
					// 预登录失败
					// 不显示一键登录选项（或置灰）
					// 根据错误信息判断失败原因，如有需要可将错误提交给统计服务器
					this.oneKeyLoginFlag = false
					this.maskPhone = ""
					this.token = ""
					console.log('预登录失败', res)
				}
			})
			// const token = uni.getStorageSync('token')
			// if (token) {
			// 	uni.request({
			// 		url: BASE_URL + '/api/user/info',
			// 		method: 'get',
			// 		header: {
			// 			'version_type': uni.getSystemInfoSync().platform, //android或者ios
			// 			'edition_number': uni.getStorageSync('versionCode'), // 打包时manifest设置的版本号
			// 			'content-type': 'application/json',
			// 			'Authorization': 'Bearer ' + uni.getStorageSync('token')
			// 		},
			// 		success(res) {
			// 			console.log(res);
			// 			const req = res.data
			// 			if (req.code == 200) {
			// 				uni.reLaunch({
			// 					url: '/pages/index/index'
			// 				})
			// 			}
			// 		}
			// 	})
			// }
		},
		onShow() {
			// getApp().update()
			// uni.authorize({
			// 	scope: 'scope.userLocation',
			// 	success: ()=>{
			// 		console.log('用户已授权定位权限')
			// 	},
			// 	fail: err => {
			// 		console.log('用户已拒绝定位权限', res)
			// 	}
			// })
			if (!uni.getStorageSync("user_agreement")) {
				console.log("跳转");
				uni.navigateTo({
					url: "/pages/userAgreement/index",
				});
			}
		},
		computed: {
			flag() {
				return (
					this.username.length === 11 &&
					((this.swiperIndex == 0 && this.code.length === 6) ||
						(this.swiperIndex == 1 && this.realPwd.length > 0))
				);
			},
		},
		watch: {
			swiperIndex: {
				immediate: true,
				handler() {
					this.btnName = ["注册/登录", "登录"][this.swiperIndex];
				},
			},
		},
		methods: {
			oneKeyLogin() {
                if (!this.checked)
					return this.toast("请先阅读并同意用户服务协议和隐私政策");
				const that = this
				uni.login({
					provider: "univerify",
					univerifyStyle: {
						// 自定义登录框样式
						//参考`univerifyStyle 数据结构`
						//具体样式设计请去uni-app文档查看
						//不填写任何自定义登录框样式的话就会采取默认样式
					},
					success(res) {
						// 登录成功
						const {openid, access_token} = res.authResult
						if(res && res.authResult){
							that.login("/auth/uni-verify", {
								access_token,
								openid
							})
							setTimeout(() => {
								uni.closeAuthView(); //关闭一键登录弹出窗口
							}, 500);
						}
						
					},
					fail(res) {
						// 登录失败
						if(res){
							uni.showToast({
								title:res.errMsg || '获取手机号信息失败',
								icon:'none'
							})
						} else {
							uni.showToast({
								title: '获取手机号信息失败',
								icon:'none'
							})
						}
                        setTimeout(() => {
							uni.closeAuthView(); //关闭一键登录弹出窗口
						}, 500);
					},
				});
			},
			otherPhoneLogin() {
				// 跳转到手机号登录页
				this.otherLogin = true;
			},
			wechatLogin() {
				// 微信登录逻辑
				this.otherLogin = false;
			},
			toPwd() {},
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top",
				});
			},

			login(path, params) {
				const platform = uni.getSystemInfoSync().platform;
				console.log("platform", platform);
				const info = plus.push.getClientInfo();
				path = path || (["/auth/login-by-phone-code", "/auth/login-by-phone-pass"][
					this.swiperIndex
				]);
				params = params || {
					phone: this.username,
					captcha: config.BASE_FLag ? this.code : "123", //正式使用这个
					password: this.realPwd, //正式使用这个
					captchaId: "",
				};
				params.cid = info.clientid
				this.$http.post(path, params).then((response) => {
					console.log("登录状态", response);
					if (response.code == 200) {
						uni.setStorageSync("token", response.message.AccessToken);
						uni.setStorageSync("RefreshToken", response.message.RefreshToken);
						/**
						 * 	本机用户 位置 电量 上报
						 */
						// #ifdef APP
						this.$http.get("/api/user/info").then((res) => {
							uni.setStorageSync("nickname", res.message.user_info.nickname);
							uni.setStorageSync("switch", res.message.switch);
							uni.setStorageSync("avatar", res.message.user_info.avatar);
							uni.setStorageSync("business", res.message.user_info.is_business);
							uni.setStorageSync("userInfo", res.message.user_info);
							uni.setStorageSync("im", {
								token: res.message.user_info.im_token,
								account: res.message.user_info.im_id,
							});
							setTimeout(() => {
								this.$store.dispatch("SET_Yxim");
								// this.$store.dispatch(
								// 	'GET_REMARK_LIST'
								// )
							}, 500);
							uni.setStorageSync(
								"userLocation",
								res.message.geo_info.formatted_address
							);
						});
						this.toast("登陆成功");
						setTimeout(() => {
							uni.setStorageSync(
								"firstRegister",
								!!response.message.firstRegister
							);
							if (response.message.firstRegister) {
								return uni.redirectTo({
									url: "/pages/firstLogin/firstLogin",
								});
							} else {
								// #ifdef APP-PLUS
								uni.redirectTo({
									url: "/pages/index/index",
								});
								// #endif
							}
						}, 1000);
						// this.$common.getBattery(level => {
						// 	uni.getLocation({
						// 		type: 'wgs84',
						// 		highAccuracyExpireTime: 30000,
						// 		isHighAccuracy: true,
						// 		success: async res => {
						// 			const gcj02 = wgs84_to_gcj02(res.longitude, res
						// 				.latitude)
						// 			uni.setStorageSync('location', res.longitude + ',' + res
						// 				.latitude)
						// 			const result = await apiLocationReport({
						// 				longitude: gcj02[0], //经度
						// 				latitude: gcj02[1], //维度
						// 				electricity: level //电量 0-100
						// 			}).then(req => {
						// 				this.$http.get('/api/user/info').then(res => {
						// 					uni.setStorageSync('nickname', res
						// 						.message.user_info.nickname
						// 					)
						// 					uni.setStorageSync('switch', res
						// 						.message.switch
						// 					)
						// 					uni.setStorageSync('avatar', res
						// 						.message.user_info.avatar);
						// 					uni.setStorageSync('business', res
						// 						.message.user_info
						// 						.is_business)
						// 					uni.setStorageSync('userInfo', res
						// 						.message.user_info)
						// 					uni.setStorageSync('im', {
						// 						token: res.message
						// 							.user_info
						// 							.im_token,
						// 						account: res.message
						// 							.user_info.im_id
						// 					})
						// 					console.log(
						// 						'=====login===store=========',
						// 						res.message
						// 						.user_info.im_id);
						// 					setTimeout(() => {
						// 						this.$store.dispatch(
						// 							'SET_Yxim')
						// 						// this.$store.dispatch(
						// 						// 	'GET_REMARK_LIST'
						// 						// )
						// 					}, 500)
						// 					// this.$store.dispatch(
						// 					// 	'SET_Yxim')
						// 					// this.$store.dispatch(
						// 					// 	'GET_REMARK_LIST'
						// 					// )
						// 					uni.setStorageSync('userLocation',
						// 						res.message.geo_info
						// 						.formatted_address)
						// 				})
						// 				this.toast('登陆成功');
						// 				console.log('-------firstRegister---------',
						// 					response);
						// 				setTimeout(() => {
						// 					uni.setStorageSync('firstRegister',
						// 						!!response
						// 						.message.firstRegister)
						// 					if (response.message
						// 						.firstRegister) {
						// 						return uni.redirectTo({
						// 							url: '/pages/firstLogin/firstLogin'
						// 						})
						// 					} else {
						// 						uni.redirectTo({
						// 							url: "/pages/index/index"
						// 						})
						// 					}
						// 				}, 1000)

						// 				// console.log( 'success---本机用户 位置 电量 上报',res);
						// 				fn && fn();
						// 			});
						// 		},
						// 		fail: err => {
						// 			console.log('没有权限', err, 'aaaa');
						// 			// if (err.errMsg.indexOf('getLocation:fail') > -1) {
						// 			// 	this.navigateTo({
						// 			// 		url: '/pages/permissions/permissions',
						// 			// 		success: res => {},
						// 			// 		fail: () => {},
						// 			// 		complete: () => {}
						// 			// 	});
						// 			// }
						// 			this.login()
						// 		}
						// 	});
						// });
						// #endif
						// #ifdef H5
						this.$http.get("/api/user/info").then((res) => {
							uni.setStorageSync("nickname", res.message.user_info.nickname);
							uni.setStorageSync("userInfo", res.message.user_info);
							uni.setStorageSync("avatar", res.message.user_info.avatar);
							uni.setStorageSync("im", {
								token: res.message.user_info.im_token,
								account: res.message.user_info.im_id,
							});
							uni.setStorageSync(
								"userLocation",
								res.message.geo_info.formatted_address
							);
							// this.$store.dispatch('SET_Yxim')
							// this.$store.dispatch(
							// 	'GET_REMARK_LIST')
							setTimeout(() => {
								this.$store.dispatch("SET_Yxim");
								this.$store.dispatch("GET_REMARK_LIST");
							}, 1000);
						});
						this.toast("登陆成功");
						uni.setStorageSync("firstRegister", !!res.message.firstRegister);
						setTimeout(() => {
							if (res.message.firstRegister) {
								return uni.redirectTo({
									url: "/pages/firstLogin/firstLogin",
								});
							} else {
								uni.redirectTo({
									url: "/pages/index/index",
								});
							}
						}, 1000);

						// #endif
					}
				});
			},
			easyLogin() {
				const path = ["/auth/login-by-phone-code", "/auth/login-by-phone-pass"][
					this.swiperIndex
				];
				this.$http
					.post(path, {
						phone: this.username,
						// "password": this.pwd, //正式使用这个
						// "captcha": this.code, //正式使用这个
						captcha: "123", //测试使用这个
						captchaId: "",
					})
					.then((response) => {
						if (response.code == 200) {
							uni.setStorageSync("token", response.message.AccessToken);
							uni.setStorageSync("RefreshToken", response.message.RefreshToken);
							/**
							 * 	本机用户 位置 电量 上报
							 */
							// #ifdef APP
							this.$common.getBattery((level) => {
								this.$http.get("/api/user/info").then((res) => {
									uni.setStorageSync("nickname", res.message.user_info.nickname);
									uni.setStorageSync("switch", res.message.switch);
									uni.setStorageSync("pemission", 1);
									uni.setStorageSync("avatar", res.message.user_info.avatar);
									uni.setStorageSync(
										"business",
										res.message.user_info.is_business
									);
									uni.setStorageSync("userInfo", res.message.user_info);
									uni.setStorageSync("im", {
										token: res.message.user_info.im_token,
										account: res.message.user_info.im_id,
									});

									setTimeout(() => {
										this.$store.dispatch("SET_Yxim");
									}, 500);

									uni.setStorageSync(
										"userLocation",
										res.message.geo_info.formatted_address
									);
									setTimeout(() => {
										uni.setStorageSync(
											"firstRegister",
											!!response.message.firstRegister
										);
										if (response.message.firstRegister) {
											return uni.redirectTo({
												url: "/pages/firstLogin/firstLogin",
											});
										} else {
											uni.redirectTo({
												url: "/pages/index/index",
											});
										}
									}, 1000);

									// 	// console.log( 'success---本机用户 位置 电量 上报',res);
									// 	fn && fn();
								});
							});
							// #endif
						}
					});
			},
			locaTy() {
				this.$refs.locationPopup.close("center");
				this.login();
			},
			locaQuit() {
				this.$refs.locationPopup.close("center");
				plus.runtime.quit();
				return true;
			},
			goNav(url) {
				console.log("=========url===========", url);
				this.navigateTo({
					url,
				});
			},
			goLogin() {
				if (!this.flag) return;
				if (!this.checked)
					return this.toast("请先阅读并同意用户服务协议和隐私政策");
				this.login();
				// // #ifdef H5
				// this.login()
				// return
				// // #endif
				// const appAuthorizeSetting = uni.getAppAuthorizeSetting()
				// if (appAuthorizeSetting.locationAuthorized !== 'authorized') {
				// 	this.$refs.locationPopup.open('center')
				// 	return false
				// } else {
				// 	this.login()
				// }
			},
			setCurrent(idx) {
				this.swiperIndex = idx.detail.current;
			},
			getCheckNum() {
				if (this.codeTime > 0) {
					this.toast("不能重复获取");
					return;
				} else {
					if (this.username.length != 11) return this.toast("请输入正确的手机号");

					this.$http.post("/auth/apply-phone-login-sms", {
						phone: this.username,
					});
					this.codeTime = 60;
					let timer = setInterval(() => {
						this.codeTime--;
						if (this.codeTime < 1) {
							clearInterval(timer);
							this.codeTime = 0;
						}
					}, 1000);
				}
			},
			// 切换密码显示/隐藏
			togglePasswordVisibility() {
				// 切换显示状态
				this.showPassword = !this.showPassword;

				// 根据显示状态更新输入框的值
				if (this.showPassword) {
					// 显示模式：显示实际密码
					this.inputValue = this.realPwd;
				} else {
					// 隐藏模式：显示圆点
					this.inputValue = "●".repeat(this.realPwd.length);
				}
			},
			handlePasswordInput(value) {
				if (this.showPassword) {
					// 显示模式：直接更新实际密码
					this.realPwd = value;
				} else {
					// 隐藏模式：需要特殊处理

					// 判断是输入还是删除
					if (value.length > this.realPwd.length) {
						// 输入：取最后一个字符添加到实际密码
						const newChar = value.slice(this.realPwd.length);
						this.realPwd += newChar;
					} else {
						// 删除：更新实际密码长度
						this.realPwd = this.realPwd.substring(0, value.length);
					}

					// 延迟更新显示，避免干扰输入
					this.$nextTick(() => {
						this.inputValue = "●".repeat(this.realPwd.length);
					});
				}
			},
		},
	};
</script>

<style lang="scss" scoped>
	.forgotPasswords {
		font-weight: 700;
		font-size: 32rpx;
		background: linear-gradient(2.5764389898695255deg, #4bc6ed 0%, #bc93f2 100%);
		-webkit-background-clip: text;
		/* 使用文字作为背景裁剪 */
		color: transparent;
		/* 使文字透明，只显示背景 */
	}

	.bottomBtn {
		display: flex;
		justify-content: center;
		margin-top: 64rpx;
	}

	.areaCode {
		width: 145rpx;
		min-width: 145rpx;
		height: 110rpx;
		line-height: 110rpx;
		text-align: center;
		font-weight: 900;
		font-size: 42rpx;
		margin-right: 22rpx;
		border-top-right-radius: 0 !important;
		border-bottom-right-radius: 0 !important;
	}

	.baStype {
		text-align: center;
		// width: 750rpx;
		margin-top: 50rpx;
		// display: flex;
		// justify-content: center;
		color: #666;
		// position: fixed;
		// bottom: 30rpx;
		z-index: 9999;
	}

	.inputRadio {
		flex: 1;
		width: 100%;
		padding: 0 66rpx !important;
		border-top-left-radius: 0 !important;
		border-bottom-left-radius: 0 !important;
	}

	.location-content {
		width: 540rpx;
		margin: auto;
		background-color: #1a1a1a;
		border-radius: 20rpx;
		color: #fff;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 60rpx 40rpx;

		.loca-name {
			font-size: 32rpx;
			font-weight: bold;
			margin-bottom: 20rpx;
			text-align: center;
		}

		.loca-tip {
			width: 100%;
			text-align: left;
			font-size: 28rpx;
			margin-bottom: 10rpx;
		}

		.loca-txt {
			font-size: 24rpx;
		}

		.loca-ty {
			padding: 20rpx 150rpx;
			color: #333;
			margin-top: 100rpx;
			background-color: #fff;
			border-radius: 60rpx;
		}

		.loca-quit {
			margin-top: 40rpx;
			font-weight: bold;
			font-size: 28rpx;
		}
	}

	.xy {
		// background: green;
		z-index: 99999999;
		// height: 130rpx;
		// width: 300rpx;
		position: relative;
		background-image: linear-gradient(to right, #4bc6ed, #bc93f2);
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
	}

	.check {
		// background: red;
		display: flex;
		font-size: 24rpx;
		font-family: Source Han Sans-Regular, Source Han Sans;
		font-weight: 400;
		color: #888888;
		line-height: 35rpx;
		margin-top: 102rpx;
		justify-content: center;
	}

	.login {
		// width: 622rpx;
		height: 110rpx;
		line-height: 110rpx;
		margin-top: 80rpx;
		border-radius: 14rpx;
		font-size: 32rpx;
		text-align: center;
		background: linear-gradient(#4bc6ed, #bc93f2);
	}

	.btn {
		font-size: 28rpx;
		margin-left: 24rpx;
		margin-top: 22rpx;
	}

	.appPage {
		position: relative;
		width: 100vw;
		height: 100vh;
	}

	.uni-input {}

	/deep/ .uni-input-input {
		color: #fff;
		font-weight: 900;
	}

	.uni-margin-wrap {
		// width: 622rpx;

		// .swiper {
		// 	height: 97rpx;
		// }
	}

	.inputBg {
		color: #fff;
		background: #272928;
		// height: 97rpx;
		display: flex;
		text-align: center;
		justify-content: center;
		align-items: center;
		border-radius: 16rpx;
		// padding: 0 24rpx;
		margin-bottom: 42rpx;
	}

	.content {
		max-width: 670rpx;
		margin: 0 40rpx;
		position: absolute;
		z-index: 1;
		top: 665rpx;
		left: 0;
		right: 0;
		bottom: 0;
	}

	.bg {
		width: 100%;
		height: 100%;
		position: absolute;
		// background-image: url('../../static/images/index/bg.png');
		// background-size: cover;
		// background-repeat: no-repeat;
	}

	.codoColor {
		// color: rgba(203, 207, 212, 0.62);
		margin-left: 15rpx;
		font-weight: 700;
		font-size: 32rpx;
		background: linear-gradient(2.5764389898695255deg, #4bc6ed 0%, #bc93f2 100%);
		-webkit-background-clip: text;
		background-clip: text;
		color: transparent;
	}

	.password-display {
		height: 70rpx;
		line-height: 70rpx;
		color: #fff;
		font-weight: 900;
	}

	.password-display:empty:before {
		content: attr(placeholder);
		color: rgba(255, 255, 255, 0.6);
	}

	.custom-cursor {
		width: 2rpx;
		height: 32rpx;
		background-color: #fff;
		position: absolute;
		top: 50%;
		transform: translateY(-50%);
		right: -10rpx;
		animation: blink 1s infinite;
	}

	.login-wrap-new {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: space-between;
	}

	.phone-text {
		font-size: 46rpx;
		color: #ffffff;
		line-height: 46rpx;
		font-style: normal;
		text-align: center;
		font-weight: bold;
	}

	.main-login-btn {
		width: 80vw;
		margin-top: 86rpx;
		height: 100rpx;
		border-radius: 16rpx;
		background: linear-gradient(90deg, #ad60ff 0%, #57c7ff 100%);
		font-size: 27rpx;
		color: #ffffff;
		line-height: 100rpx;
		box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
	}

	.login-types {
		position: absolute;
		bottom: 96rpx;
	}

	.other-login-row {
		display: flex;
		flex-direction: row;
		justify-content: center;
		margin-bottom: 16rpx;
	}

	.other-login-btn {
		width: 92rpx;
		height: 92rpx;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 0 24rpx;

		&.phone {
			background: rgba(210, 210, 210, 0.25);
		}

		&.wechat {
			background: #30a71d;
		}
	}

	.icon {
		width: 48rpx;
		height: 48rpx;
	}

	.other-login-tip {
		color: #fff;
		font-size: 24rpx;
		margin-top: 12rpx;
		opacity: 0.8;
		text-align: center;
	}

	@keyframes blink {

		0%,
		100% {
			opacity: 1;
		}

		50% {
			opacity: 0;
		}
	}
</style>