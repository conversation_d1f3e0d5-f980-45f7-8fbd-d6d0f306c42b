import App from "./App";
import http from "@/utils/request.js";
Vue.prototype.$http = http;
// #ifndef VUE3
import Vue from "vue";
import common from "@/utils/common.js";
import {
	navigateTo
} from "@/utils/common.js";
import {
	style
} from "@/style.js";
import {
	addPermisionInterceptor,
	removePermisionInterceptor,
} from "@/uni_modules/x-perm-apply-instr/js_sdk/index.js";

addPermisionInterceptor(
	"chooseImage",
	"为了修改个人头像和发布信息图片视频等, 我们需要申请您设备的相机和存储权限"
);
addPermisionInterceptor(
	"chooseVideo",
	"打开「照片」发布动态、上传相册。 你已关闭LightingBall照片访问权限，建议允许访问「所有照片」"
);
addPermisionInterceptor(
	"saveImageToPhotosAlbum",
	"为了保存照片或二维码到手机相册, 我们需要申请您设备的存储权限"
);
addPermisionInterceptor(
	"getLocation",
	"开启位置权限以访问LightingBall地图功能，你可以在「设置」内随时关闭此权限。"
).then(res => {
	console.log("开启定位:", res)
	// #ifdef APP-PLUS
	// Android
	if (res === 0) {
		uni.navigateTo({
			url: "/pages/index/locationGuide"
		})
	} 
	// #endif
}).catch(err => {
	uni.navigateTo({
		url: '/pages/permissions/permissions'
	})
});
addPermisionInterceptor(
	"makePhoneCall",
	"为了联系客服/用户/咨询等, 我们需要申请您设备的拨打电话权限"
);
addPermisionInterceptor(
	"getRecorderManager",
	"为了使用语言消息功能等, 我们需要申请您设备的麦克风权限"
);
addPermisionInterceptor(
	"startLocationUpdate",
	"开启位置权限以访问LightingBall地图功能，你可以在「设置」内随时关闭此权限。"
);
addPermisionInterceptor(
	"scanCode",
	"LightingBall请求允许访问「相机」、「媒体内容」和「文件」权限以使用扫一扫等功能。"
);
Vue.prototype.navigateTo = navigateTo;
Vue.prototype.$common = common;
Vue.prototype.$style = style;
import NIMSDK from "nim-web-sdk-ng/dist/NIM_UNIAPP_SDK";
import store from "@/store/index.js";
Vue.prototype.nim = NIMSDK;
Vue.prototype.$store = store;
import "./uni.promisify.adaptor";
Vue.config.productionTip = false;
App.mpType = "app";
const app = new Vue({
	store,
	...App,
});
// main.js
plus.navigator.closeSplashscreen();
import uView from "uview-ui";
Vue.use(uView);
app.$mount();
// #endif

// import NIM from 'nim-web-sdk-ng/dist/NIM_UNIAPP_SDK'
// import min from 'nim-web-sdk-ng/dist/NIM_UNIAPP_SDK'
// Vue.prototype.NIM = NIM

// IM
// const NIM = require('utils/NIM_MINIAPP_SDK')
// 聊天室
// const Chatroom = require('utils/Chatroom_MINIAPP_SDK')
// #ifdef VUE3
import {
	createSSRApp
} from "vue";
export function createApp() {
	const app = createSSRApp(App);
	return {
		app,
	};
}
// #endif