<template>
	<view class="">
		<view class="appPagess">
			<image class="bg" :src="info.user_info? info.user_info.ext.cover:''" mode="aspectFill" />

			<view class="head">
				<view style="z-index: 999;" @click="goBack">
					<uni-icons type="left" color="#000" size="27"></uni-icons>
				</view>
				<view style="display: flex;" v-if="info.user_info && info.user_info.relation!=4">
					<image class="rightIcon" @click="getLocation" src="../../static/images/my/myadd.png" mode=""
						style="margin-right: 40rpx;">
					</image>
					<image class="rightIcon" @click="more" src="../../static/images/more.png" mode=""></image>
				</view>
			</view>
			<view class="body">
				<view style="padding: 0 32rpx;">
					<view class="ava">
						<image class="avatar" :src="info.user_info?info.user_info.avatar:''" mode="aspectFill">
						</image>
						<view class="t_display">
							<image @click="goNavChat()" class="edit" v-if="info.user_info && info.user_info.relation!=4"
								src="../../static/images/xinfeng.png" mode="aspectFill">
							</image>
							<view class="btn" @click="setFollow(info.user_info.relation,info.user_info.uuid)"
								v-if="info.user_info.relation!=4">
								<!-- v-if="info.user_info && ![3,4].includes(info.user_info.relation)" -->
								{{["关注","已关注","互相关注","回关"][info.user_info.relation] }}
							</view>
						</view>
					</view>
					<view class="name" v-if="info.user_info">
						{{info.user_info.nickname}}
						<image class="img32 logo" :src="'../../static/images/vip/vipLogo'+vipLevel+'.png'" mode="">
						</image>
					</view>
					<view class="idInfo" v-if="info.user_info">
						ID:{{info.user_info.uuid}}
					</view>
					<view class="introduces" style="margin-top: 20rpx;width: 686rpx;" v-if="info.user_info">
						{{info.user_info.ext.introduction}}
					</view>
					<view class="t_display" style="margin-top: 16rpx;">
						<view class="t_display" style="margin-right: 42rpx;" v-if="info.geo_info.province">
							<image class="icon" src="../../static/images/address.png" mode=""></image>
							<view class="introduce" v-if="info.geo_info">
								{{info.geo_info.province}}
							</view>
						</view>
						<view class="t_display" style="margin-right: 42rpx;" v-if="info.user_info.ext.birthday">
							<image class="icon" src="../../static/images/birthday.png" mode=""></image>
							<view class="introduce" v-if="info.user_info">
								{{info.user_info.ext.birthday}}
							</view>
						</view>
					</view>
					<view class="t_display" style="margin-top: 20rpx;" v-if="info.user_info.home_town_format">
						<image class="icon" src="../../static/images/map.png" mode=""></image>
						<view class="introduce" style="width: 500rpx;">
							来自于 {{info.user_info.home_town_format}}
						</view>
					</view>
					<view class="t_display" style="margin-top: 16rpx;" v-if="info.relation">
						<view class="t_display" style="margin-right: 26rpx;">
							<view>{{info.relation.follow}}</view>
							<view class="introduce">关注</view>
						</view>
						<view class="t_display" style="margin-right: 26rpx;">
							<view>{{info.relation.fans}}</view>
							<view class="introduce">粉丝</view>
						</view>
						<view class="t_display">
							<view>{{info.relation.friend}}</view>
							<view class="introduce">朋友</view>
						</view>
					</view>
					<view v-if="imgArr" style="font-size: 26rpx;margin-top: 28rpx;">
						相册
					</view>
					<view class="">
						<scroll-view class="scroll-view_H" scroll-x="true">
							<view class="scroll-view-item_H" v-for="(src,index) in imgArr" :key="index">
								<image class="img" :src="src.photoPath" mode="aspectFill" @click="preview(index)">
								</image>
							</view>
						</scroll-view>
					</view>
				</view>
				<u-sticky :enable="false" bg-color="transparent" @fixed="setFixed" @unfixed="unfixed">
					<Tabs :tabs="tabList" :current="swiperCurrent" @setCurrent="tabsChange" />
				</u-sticky>
				<view class="">
					<view class="" v-if="swiperCurrent==0">
						<view class="nullMoment" v-if="!dataArrS.length">
							<image class="img124" src="../../static/images/wudongtai.png" mode=""></image>
							<view class="tit">还没有发布作品</view>
						</view>
						<view class="" v-else style="overflow: hidden;">
							<Post :list="dataArrS" @more="more" @scrolltolower="scrolltolowerS" :previewFlag="true"
								@share="share" @setLike="setLikeS" @goAddress="goNavLoaction"
								:goFLag="swiperCurrent==0"></Post>
							<view class="" style="height: 100rpx;" />
						</view>
					</view>
					<view class="" v-else-if="swiperCurrent==1" style="overflow: hidden;">
						<Post @scrolltolower="scrolltolowerB" :list="dataArrB" @more="more" :previewFlag="true"
							@share="share" @setLike="setLikeB" @goAddress="goNavLoaction" :uuid="info.user_info.uuid">
						</Post>
						<view class="" style="height: 100rpx;" />
					</view>
				</view>

				<SeePopup ref="seePopup" @choose="seePopChoose"></SeePopup>
				<More ref="morePopup" :popupInfo="info"></More>
				<Choose v-if="info.user_info" ref="choosePopup" :current="info.ghost_mode" :uid="info.user_info.uid"
					:uuid="info.user_info.uuid" @switch="updateInfo">
				</Choose>
				<sharePopup ref="share" :post="shareItem"></sharePopup>
			</view>
		</view>
		<u-toast ref='notify' />
		<vipTips ref="vipTips" :imgCurrent="imgCurrent" @confirm="confirm" />

	</view>
</template>
<script>
	import Post from "@/components/post/post.vue"
	import More from "../mainText/components/more.vue"
	import Choose from "../otherPage/components/choose.vue"
	import Tabs from "../my/components/tabs.vue"
	import SeePopup from "../my/components/seeLook.vue"
	export default {
		components: {
			Tabs,
			Choose,
			Post,
			More,
			SeePopup,
		},
		data() {
			return {
				imid: "",
				dataArrS: [],
				pageS: 1,
				totalS: true,
				pageB: 1,
				totalB: true,
				uuid: "",
				fixed: true,
				scrollTop: 0, // 页面滚动距离
				dataArr: [],
				swiperCurrent: 0,
				barStyle: {
					height: "1px",
					background: "linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%)",
				},
				tabList: [{
					name: '动态'
				}, {
					name: '喜欢'
				}],
				ImgUrl: 'https://lb-1317942045.cos.ap-shanghai.myqcloud.com/',
				info: {
					geo_info: {
						province: ''
					},
					user_info: {
						uuid: "",
						relation: "",
						nickname: "",
						ext: {
							cover: "",
							birthday: ""
						}
					},
					ext: {
						cover: ""
					},
					relation: {}
				},
				statusBarHeight: 0,
				imgCurrent: 0,
				vipLevel: "",
				imgArr: [],
				photoArr: [],
				uploadArr: [],
				list: [{
					name: '动态'
				}, {
					name: '喜欢'
				}],
				dataArrB: [],
				// 因为内部的滑动机制限制，请将tabs组件和swiper组件的current用不同变量赋值
				current: 0, // tabs组件的current值，表示当前活动的tab选项
				swiperCurrent: 0, // swiper组件的current值，表示当前那个swiper-item是活动的
				shareItem: {},
			}

		},
		async onLoad(options) {
			// const system = uni.getStorageSync('system')
			if (options.imId) {
				let imid = options.imId.split('p2p-')
				await this.$http.get('/share/user/im/' + imid[1]).then(res => {
					this.uuid = res.message.user_info.uuid
				})
			} else {
				this.uuid = options.uuid
			}
			// this.statusBarHeight = JSON.parse(system).statusBarHeight + 20
			this.getData()
		},

		methods: {
			vipOpen() {
				this.imgCurrent = 2
				this.$refs.vipTips.open('center')
			},
			goNavLoaction(item) {
				setTimeout(() => {
					uni.navigateBack({
						delta: 1
					})
					const pages = getCurrentPages(); //获取页面栈
					const beforePage = pages[pages.length - 1]; //前一个页面
					beforePage.$vm.goAddress(item); //前一个页面方法
				}, 500)
			},
			toast(title) {
				this.$refs.notify.show({
					title,
					position: "top"
				})
			},
			goNavChat() {
				let params = {
					account: this.info.user_info.im_id,
					chatHeadImg: this.info.user_info.avatar,
					chatName: this.info.user_info.nickname,
					uid: 'p2p-' + this.info.user_info.im_id,
					roomtype: 'p2p',
					nuck: this.info.user_info.nickname,
				}
				uni.navigateTo({
					url: "/pages/HM-chat/HM-chat?userItem=" + encodeURIComponent(JSON.stringify(params))
				})
			},
			getDynamic() {
				this.$http.get('/api/moment/user/list', {
					uid: this.info.user_info.uid,
					page: this.pageS,
					size: 10
				}).then(res => {
					this.dataArrS.push(...res.message)
					this.totalS = !!res.message.length
				})
			},
			share(item) {
				this.shareItem = item
				this.$refs.share.open()
			},
			scrolltolowerS() {
				if (this.totalS) {
					this.pageS += 1;
					this.getDynamic()
				}
			},
			scrolltolowerB() {
				if (this.totalB) {
					this.pageB += 1;
					this.getLikeInfo()
				}
			},
			getLocation() {
				this.$refs.choosePopup.open()
			},
			setLikeS(item) {
				this.$http.post('/api/moment/like', {
					momentId: item.momentId,
					like: item.isLike ? 2 : 1
				}).then(res => {
					this.$set(this.dataArrS, item.index, {
						...this.dataArrS[item.index],
						is_like: !item.isLike,
						like: this.dataArrS[item.index].like + (!item.isLike ? 1 : -1)
					})
				})
			},
			setLikeB(item) {
				this.$http.post('/api/moment/like', {
					momentId: item.momentId,
					"like": item.isLike ? 2 : 1
				}).then(res => {
					this.$set(this.dataArrB, item.index, {
						...this.dataArrB[item.index],
						is_like: !item.isLike,
						like: this.dataArrB[item.index].like + (!item.isLike ? 1 : -1)
					})
				})
			},

			getLikeInfo() {
				this.$http.get('/api/moment/user/like/list', {
					uid: this.info.user_info.uid,
					page: this.pageB,
					size: 10
				}).then(res => {
					this.dataArrB.push(...res.message)
					this.totalB = !!res.message.length
				})
			},
			getData() {
				this.$http.get('/share/user/' + this.uuid).then(res => {
					this.info = res.message
					this.vipLevel = res.message.user_info.vip_level
					this.getPhoto()
					this.getDynamic()
					this.getLikeInfo()
				})
			},
			preview(current) {
				uni.previewImage({
					current,
					urls: this.photoArr,
				});
			},
			updateInfo(flag) {
				console.log('flag', flag);
				if (!flag) {
					this.vipOpen()
				}
				this.$http.get('/share/user/' + this.uuid).then(res => {
					this.info = res.message
				})

			},
			//current:0 关注某人  1 取消关注
			//0陌生人 1是我关注的  2是互相关注的 3关注我的 4我自己 
			setFollow(current, uuid) {
				if ([0, 3].includes(current)) {
					this.$http.post('/api/user/follow/add', {
						uuid
					}).then(res => {
						this.$http.get('/share/user/' + this.uuid).then(res => {
							this.info = res.message
						})
					})
				} else if ([1, 2].includes(current)) {
					this.$http.post('/api/user/follow/del', {
						uuid
					}).then(res => {
						this.$http.get('/share/user/' + this.uuid).then(res => {
							this.info = res.message
						})
					})
				}
			},

			goBack() {
				uni.navigateBack()
			},
			more() {
				this.$refs.more.open()
			},
			seePopChoose() {

			},
			first() {
				this.$refs.morePopup.close()
				this.$refs.seePopup.open()
			},
			more(item) {
				this.$refs.morePopup.open()
			},
			unfixed(ids) {
				this.fixed = false
			},
			setFixed(flag) {
				this.fixed = true
			},
			// swiper-item左右移动，通知tabs的滑块跟随移动
			transition(e) {
				let dx = e.detail.dx;
				this.$refs.uTabs.setDx(dx);
			},
			// 由于swiper的内部机制问题，快速切换swiper不会触发dx的连续变化，需要在结束时重置状态
			// swiper滑动结束，分别设置tabs和swiper的状态
			animationfinish(e) {
				let current = e.detail.current;
				this.$refs.uTabs.setFinishCurrent(current);
				this.swiperCurrent = current;
				this.current = current;
			},
			// scroll-view到底部加载更多
			onreachBottom() {

			},
			// tabs通知swiper切换
			tabsChange(index) {
				this.swiperCurrent = index;
			},
			delImg(id) {
				this.$http.post('/api/user/del-photo', {
					id
				}).then(res => {
					this.getPhoto()
				})
			},
			getPhoto() {
				this.$http.get('/api/user/get-photo', {
					uid: this.info.user_info.uid
				}).then(res => {
					this.imgArr = res.message
					this.photoArr = res.message.map(item => item.photoPath)
				})
			},
			goNav(url) {
				let params = {
					account: this.info.user_info.im_id,
					chatHeadImg: this.info.user_info.avatar,
					chatName: this.info.user_info.nickname,
					uid: 'p2p-7r43w6fv1art7g',
					roomtype: 'p2p'
				}
				uni.navigateTo({
					url: url + "?userItem=" + encodeURIComponent(JSON.stringify(params))
				})
			},
			upload() {
				uni.chooseImage({
					count: 9,
					success: async (chooseImageRes) => {
						const tempFilePaths = chooseImageRes.tempFilePaths;
						let arr = []
						await Promise.all(tempFilePaths.map(async (item) => {
							const img = await this.$common.uploads(item, {
								type: 4
							})
							arr.push({
								"photo_path": img,
								"id": 0
							})
						}, ))
						this.$http.post('/api/user/add-photo', {
							photos: arr
						}).then(res => {
							this.getPhoto()
						})

					}
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
	.nullMoment {
		width: 750rpx;
		height: 550rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;

		.momentBtn {
			width: 228rpx;
			height: 70rpx;
			line-height: 70rpx;
			background: #272727;
			border-radius: 16rpx;
			text-align: center;
			margin-top: 32rpx;
		}

		.tit {
			font-size: 24rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			color: #A5A5A5;
			margin-top: 40rpx;
		}
	}

	.scroll-view_H {
		margin-top: 12rpx;
		white-space: nowrap;
		width: 100%;
	}

	.btn {
		width: 160rpx;
		height: 56rpx;
		line-height: 56rpx;
		background: linear-gradient(93deg, #4BC6ED 0%, #BC93F2 100%);
		border-radius: 134rpx;
		font-size: 24rpx;
		font-family: Source Han Sans CN-Medium, Source Han Sans CN;
		font-weight: 500;
		color: #FFFFFF;
		margin-left: 24rpx;
		text-align: center;
	}

	.scroll-view-item_H {
		display: inline-block;
		width: 153rpx;
		height: 153rpx;
		padding: 10rpx;
		text-align: center;
		border-radius: 14rpx;
		margin-right: 30rpx;
		position: relative;

		.img {
			width: 153rpx;
			height: 153rpx;
			border-radius: 14rpx;
		}
	}

	.close {
		width: 32rpx;
		height: 32rpx;
		position: absolute;
		right: -25rpx;
		top: -3rpx;
	}

	.icon {
		width: 24rpx;
		height: 24rpx;
		margin-right: 8rpx;
	}

	.appPagess {
		height: 60vh;
		width: 100%;
		display: flex;
		flex-direction: column;

		.picture {
			width: 100%;
			white-space: nowrap;
			margin-top: 12rpx;
			height: 153rpx;
		}

		.bg {
			width: 750rpx;
			height: 495rpx;
			position: absolute;
		}

		/* 利用flex布局, */
		.sColumn {
			display: flex;
			flex: 1;
		}

		.body {
			display: flex;
			flex: 1;
			width: 100%;
			margin-top: 290rpx;
			background-color: #191C26;
			flex-direction: column;

			.addImg {
				width: 154rpx;
				height: 154rpx;
				background: #777883;
				border-radius: 14rpx;
				display: inline-block;
				margin-right: 16rpx;
			}

			.introduceS {
				// margin-left: 10rpx;
				// white-space: nowrap;
				// text-overflow: ellipsis;
				// overflow: hidden;
				font-size: 25rpx;
				font-family: Source Han Sans-Regular, Source Han Sans;
				font-weight: 400;
				color: rgba(255, 255, 255, 0.64);
				line-height: 40rpx;
			}

			.introduce {
				margin-left: 10rpx;
				// white-space: nowrap;
				// text-overflow: ellipsis;
				// overflow: hidden;
				font-size: 25rpx;
				font-family: Source Han Sans-Regular, Source Han Sans;
				font-weight: 400;
				color: rgba(255, 255, 255, 0.64);
				line-height: 40rpx;
			}

			.idInfo {
				font-size: 26rpx;
				font-family: Source Han Sans-Medium, Source Han Sans;
				font-weight: 500;
				color: #767676;
				line-height: 40rpx;
				margin-top: 8rpx;
			}

			.name {
				font-size: 48rpx;
				font-family: Source Han Sans-Bold, Source Han Sans;
				font-weight: 700;
				color: #FFFFFF;
				margin-top: 23rpx;
				display: flex;
				align-items: center;

				.logo {
					margin-left: 32rpx;
				}
			}

			.ava {
				display: flex;
				justify-content: space-between;

				align-items: flex-end;

				.avatar {
					width: 152rpx;
					height: 152rpx;
					border-radius: 50%;
					border: 8rpx solid #191C26;
				}

				.edit {
					width: 56rpx;
					height: 56rpx;

				}
			}

		}

		.head {
			height: 100rpx;
			padding: 0 34rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-top: 95rpx;
		}

		.rightIcon {
			width: 76rpx;
			height: 76rpx;
		}
	}

	// .container {
	// 	height: 200vh;
	// }

	.sticky {
		width: 750rpx;
		height: 120rpx;
	}
</style>