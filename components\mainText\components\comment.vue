<template>
  <view>
    <view class="comment" v-for="(res, index) in dataArr" :key="res.id">
      <view class="left">
        <image
          :src="res.user_info.avatar"
          mode="aspectFill"
          @click="
            goNav('/pages/otherPage/otherPage?uuid=' + res.user_info.uuid)
          "
        ></image>
      </view>
      <view class="right">
        <view class="top">
          <view class="name">{{ res.user_info.nickname }}</view>
        </view>
        <view class="like-position">
          <view
            class="content"
            @click="
              reply(
                res.comment_id,
                res.moment_id,
                index,
                res.user_info.nickname
              )
            "
          >
            {{ res.content }}
          </view>
          <view
            class="like t_display"
            :class="{ highlight: res.isLike }"
            @click="setCommentLike(res.comment_id, res.is_like, index)"
          >
            <image
              class="img24"
              v-if="!res.is_like"
              src="@/static/images/topic/like.png"
              mode=""
            >
            </image>
            <image
              class="img24"
              v-else
              src="@/static/images/topic/like2.png"
              mode=""
            ></image>
            <view class="like_nums" style="margin-left: 20rpx">{{
              res.like
            }}</view>
          </view>
        </view>
        <view
          class="t_display"
          style="justify-content: space-between; margin-bottom: 25rpx"
        >
          <view class="times">
            {{ res.timeline }}&nbsp;&nbsp;{{ res.address }}
            <span style="margin-left: 20rpx; color: rgba(51, 53, 59, 0.8)"
              >回复</span
            >
          </view>
        </view>
        <view
          class="reply-box"
          v-if="res.little_reply.length"
          :style="{
            maxHeight: !res.little_reply.length
              ? 'none'
              : itemHeight * res.little_reply.length + 'rpx',
          }"
        >
          <view class="item" v-for="(item, ids) in res.little_reply" :key="ids">
            <view class="item-content">
              <view
                class="username t_display"
                @click="
                  reply(
                    res.comment_id,
                    res.moment_id,
                    index,
                    res.user_info.nickname,
                    item.user_info.ext.uid
                  )
                "
              >
                <image
                  class="img42"
                  :src="item.user_info.avatar"
                  mode="aspectFill"
                ></image>
                {{ item.user_info.nickname }}
              </view>
              <view
                class="text"
                style="margin-left: 56rpx"
                @click="
                  reply(
                    res.comment_id,
                    res.moment_id,
                    index,
                    res.user_info.nickname,
                    item.user_info.ext.uid
                  )
                "
                >{{ item.content }}</view
              >
              <view class="times" style="margin-left: 56rpx">
                {{ item.timeline }}
              </view>
            </view>
            <view
              class="like t_display"
              :class="{ highlight: item.is_like }"
              @click.stop="
                setReplyLike(item.reply_id, item.is_like, ids, index)
              "
            >
              {{ item.is_like }}
              <image
                class="img24 like-btn"
                v-if="!item.is_like"
                src="@/static/images/topic/like.png"
                mode=""
              ></image>
              <image
                class="img24 like-btn"
                v-else
                src="@/static/images/topic/like2.png"
                mode=""
              ></image>
              <view class="like_nums">{{ item.like }}</view>
            </view>
          </view>
        </view>
        <view
          class="all-reply t_display"
          @tap="toAllReply(res.comment_id, res.little_reply, index)"
          v-if="res.reply_total > res.little_reply.length"
        >
          <view style="width: 40rpx"></view>
          <view class="codoColor">
            展开{{ res.reply_total - res.little_reply.length }}条回复
          </view>
          <!-- <image
            class="img42"
            src="../../../static/images/themeDown.png"
            mode=""
          ></image> -->
        </view>
      </view>
      <u-toast ref="notify" />
    </view>
  </view>
</template>

<script>
export default {
  props: {
    dataArr: {
      type: Array,
      default: [],
    },
  },
  data() {
    return {
      itemHeight: 184,
      commentList: [],
    };
  },
  created() {
    // this.getComment();
  },
  methods: {
    goNav(url) {
      uni.navigateTo({
        url,
      });
    },
    //点击回复某人
    reply(commentId, momentId, index, nickname, uid = 0) {
      this.$emit("reply", {
        index,
        commentId,
        uid,
        nickname,
        momentId,
      });
    },
    toast(title) {
      this.$refs.notify.show({
        title,
        position: "top",
      });
    },
    // 跳转到全部回复
    toAllReply(commentId, little_reply, index) {
      this.$emit("getMore", {
        index,
        maxId: little_reply[little_reply.length - 1].reply_id,
        commentId,
      });
      // this.commentList[index].moreFlag = true
      // this.$forceUpdate()
    },
    setCommentLike(comment_id, isLike, index) {
      this.$emit("setCommentLike", {
        comment_id,
        isLike,
        index,
      });
    },
    // 子评论点赞
    setReplyLike(reply_id, isLike, replyIndex, parentIndex) {
      this.$emit("setReplyLike", {
        reply_id,
        isLike,
        replyIndex,
        parentIndex,
      });
    },
    // 点赞
    getLike(index, isLike, reply_id, parentIndex) {
      console.log("点赞", index, isLike, reply_id, parentIndex);

      this.$emit("setCommentContentLike", {
        index,
        isLike,
        reply_id,
        parentIndex,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.times {
  height: 35rpx;
  font-size: 24rpx;
  font-family: Source Han Sans CN-Medium, Source Han Sans CN;
  font-weight: 500;
  color: rgba(51, 53, 59, 0.6);
}

.codoColor {
  font-size: 24rpx;
  color: #0f3d79;
  padding-left: 40rpx;
  //   -webkit-background-clip: text;
  //   background: linear-gradient(93deg, #4bc6ed 0%, #bc93f2 100%);
  //   -webkit-text-fill-color: transparent;
}

.like_nums {
  margin-left: 4rpx;
  font-size: 24rpx;
  font-weight: 500;
  color: rgba(51, 53, 59, 0.9);
  line-height: 31rpx;
}

.like {
  display: flex;
  align-items: center;
  color: #9a9a9a;
  font-size: 26rpx;
  position: relative;
  z-index: 10;
}

.like-btn {
  position: relative;
  z-index: 10;
  width: 24rpx;
  height: 24rpx;
  padding: 10rpx;
  box-sizing: content-box;
  display: block;
}

.comment {
  display: flex;
  padding: 10rpx 30rpx 0 30rpx;
  //   padding-bottom: 0;

  .left {
    image {
      width: 64rpx;
      height: 64rpx;
      border-radius: 50%;
      background-color: #f2f2f2;
    }
  }

  .right {
    flex: 1;
    padding-left: 20rpx;
    font-size: 30rpx;

    .top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10rpx;

      .name {
        // color: #5677fc;
        color: rgba(51, 53, 59, 0.6);
      }

      .like {
        display: flex;
        align-items: center;
        color: #9a9a9a;
        font-size: 26rpx;
        position: relative;
        z-index: 10;
      }

      .highlight {
        color: rgba(51, 53, 59, 0.9);

        .num {
          color: rgba(51, 53, 59, 0.9);
        }
      }
    }

    .like-position {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10rpx;

      .content {
        margin-bottom: 10rpx;
        font-size: 28rpx;
        color: rgba(51, 53, 59, 0.9);
        flex: 1;
        padding-right: 20rpx;
      }
    }

    .reply-box {
      background-color: #fafafa;
      border-radius: 12rpx;
      font-size: 26rpx;
      color: rgba(51, 53, 59, 0.9);
      padding-bottom: 20rpx;
      overflow: hidden;

      .item {
        padding: 20rpx;
        padding-bottom: 10rpx;
        padding-right: 0;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;

        .item-content {
          flex: 1;
          display: flex;
          flex-direction: column;
        }

        .username {
          font-size: 28rpx;
          color: rgba(51, 53, 59, 0.9);
          margin-bottom: 10rpx;
          display: flex;
          align-items: center;

          image {
            border-radius: 280rpx;
            margin-right: 16rpx;
            width: 42rpx;
            height: 42rpx;
          }
        }

        .text {
          margin-bottom: 10rpx;
          font-size: 28rpx;
          line-height: 40rpx;
        }

        .times {
          font-size: 24rpx;
          color: rgba(51, 53, 59, 0.6);
        }

        .like {
          margin-left: 20rpx;
          padding-top: 30rpx;
        }
      }

      .all-reply {
        padding: 20rpx;
        display: flex;
        align-items: center;
        margin-top: 7rpx;
        background-color: red;

        .more {
          margin-left: 6rpx;
        }
      }
    }

    .bottom {
      margin-top: 20rpx;
      display: flex;
      font-size: 24rpx;
      color: rgba(51, 53, 59, 0.9);

      .reply {
        color: rgba(51, 53, 59, 0.9);
        margin-left: 10rpx;
      }
    }
  }
}
</style>
