{
    "name" : "LightingBall",
    "appid" : "__UNI__2DF6F7C",
    "description" : "二次元社交平台",
    "versionName" : "1.36",
    "versionCode" : 136,
    "sassImplementationName" : "node-sass",
    "transformPx" : false,
    /* 5+App特有相关 */
    "app-plus" : {
        "usingComponents" : true,
        "nvueStyleCompiler" : "uni-app",
        "compilerVersion" : 3,
        "splashscreen" : {
            "alwaysShowBeforeRender" : false,
            "waiting" : false,
            "autoclose" : true,
            "delay" : 0
        },
        "requiredPrivateInfos" : [ "getLocation" ],
        /* 模块配置 */
        "modules" : {
            "Contacts" : {},
            "Geolocation" : {},
            "Camera" : {},
            "Record" : {},
            "Payment" : {},
            "Barcode" : {},
            "Maps" : {},
            "Share" : {},
            "Push" : {},
            "OAuth" : {}
        },
        /* 应用发布信息 */
        "distribute" : {
            /* android打包配置 */
            "android" : {
                "permissions" : [
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.INJECT_EVENTS\"/>",
                    "<uses-permission android:name=\"android.permission.INSTALL_PACKAGES\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_CONTACTS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>",
                    "<uses-permission android:name=\"android.permission.REQUEST_INSTALL_PACKAGES\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.INTERNET\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CALL_PHONE\" />",
                    "<uses-permission android:name=\"android.permission.SYSTEM_ALERT_WINDOW\" />",
                    "<uses-permission android:name=\"android.permission.LOCATION\"/>",
                    //以下开始新增
                    "<uses-permission android:name=\"android.permission.RECEIVE_BOOT_COMPLETED\" />",
                    "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\" />",
                    "<uses-permission android:name=\"android.permission.ACCESS_LOCATION_EXTRA_COMMANDS\" />",
                    "<uses-permission android:name=\"android.permission.DISABLE_KEYGUARD\" />",
                    "<uses-permission android:name=\"android.permission.SCHEDULE_EXACT_ALARM\" />",
                    "<uses-permission android:name=\"android.permission.FOREGROUND_SERVICE_LOCATION\" />",
                    "<uses-permission android:name=\"android.permission.FOREGROUND_SERVICE\" />",
                    "<uses-permission android:name=\"android.permission.ACTIVITY_RECOGNITION\" />",
                    "<uses-permission android:name=\"android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS\" />",
                    "<uses-permission android:name=\"android.permission.POST_NOTIFICATIONS\" />",
                    "<uses-permission android:name=\"android.permission.ACCESS_BACKGROUND_LOCATION\" />",
                    "<uses-permission android:name=\"android.permission.BODY_SENSORS\" />"
                ],
                "abiFilters" : [ "armeabi-v7a", "arm64-v8a", "x86" ],
                "minSdkVersion" : 21,
                "schemes" : "lightingball",
                "targetSdkVersion" : 35,
                "permissionPhrase" : {
                    "android.permission.ACTIVITY_RECOGNITION" : "开启运动与健身权限，提升定位精度并记录运动状态",
                    "android.permission.ACCESS_FINE_LOCATION" : "开启位置权限以访问LightingBall地图功能，你可以在「设置」内随时关闭此权限。",
                    "android.permission.ACCESS_COARSE_LOCATION" : "开启位置权限以访问LightingBall地图功能，你可以在「设置」内随时关闭此权限。",
                    "android.permission.READ_CONTACTS" : "发现通讯录朋友 进入系统设置将「通讯录」设为开启 将朋友纳入我的地图内。",
                    "android.permission.CALL_PHONE" : "我们需要访问电话功能，以便您可以拨打电话",
                    "android.permission.CAMERA" : "打开相机使用扫一扫等功能功能",
                    "android.permission.READ_EXTERNAL_STORAGE" : "打开「照片」发布动态、上传相册。 你未开通LightingBall照片访问权限，建议允许访问「所有照片」",
                    "android.permission.WRITE_EXTERNAL_STORAGE" : "打开「照片」发布动态、上传相册。 你未开通LightingBall照片访问权限，建议允许访问「所有照片」"
                }
            },
            /* ios打包配置 */
            "ios" : {
                "urlschemewhitelist" : [ "baidumap", "iosamap", "qqmap" ],
                "dSYMs" : false,
                "UIBackgroundModes" : "location",
                "urltypes" : "lightingball",
                "privacyDescription" : {
                    "NSLocationWhenInUseUsageDescription" : "需要开启位置权限，以使用 「动态位置」功能与他人共享位置。你可以在「 设置」 中随时关闭此权限。 ",
                    "NSLocationAlwaysUsageDescription" : "应用需要始终访问您的位置信息，以便提供基于位置的服务。",
                    "NSLocationAlwaysAndWhenInUseUsageDescription" : "需要开启位置权限，以使用 「动态位置」功能与他人共享位置。你可以在「 设置」 中随时关闭此权限。 ",
                    "NSContactsUsageDescription" : "发现通讯录朋友 进入系统设置将「通讯录」设为开启 将朋友纳入我的地图内。",
                    "NSCallKitUsageDescription" : "我们需要访问电话功能，以便您可以拨打电话",
                    "NSCameraUsageDescription" : "打开相机使用扫一扫等功能功能",
                    "NSPhotoLibraryUsageDescription" : "为LightingBall打开「照片」权限用做发布动态、 上传头像等功能。 ",
                    "NSLocalNetworkUsageDescription" : "我们使用本地通知来发送重要的提醒和更新。",
                    "NSPhotoLibraryAddUsageDescription" : "应用需要访问您的照片库以保存照片或视频。",
                    "NSLocalNotificationUsageDescription" : "我们使用本地通知来发送重要的提醒和更新。",
                    // 其他已有的隐私描述
                    "NSHealthShareUsageDescription" : "",
                    // 如果你也需要写入健康数据，请同时添加以下键：
                    "NSHealthUpdateUsageDescription" : ""
                },
                "capabilities" : {
                    "entitlements" : {
                        "com.apple.developer.associated-domains" : [
                            "applinks:static-mp-72b82a91-1852-445f-bb5a-a7f5e505366c.next.bspapp.com"
                        ]
                    }
                }
            },
            /* SDK配置 */
            "sdkConfigs" : {
                "ad" : {},
                "geolocation" : {
                    "amap" : {
                        "name" : "amapOiUHXB3i",
                        "__platform__" : [ "ios", "android" ],
                        "appkey_ios" : "1b7896dd696793974201ddd5f8a8f3e4",
                        "appkey_android" : "3ef073a9b403699d7124bda4e4f6f1d4"
                    },
                    "system" : {
                        "__platform__" : [ "ios", "android" ]
                    }
                },
                "share" : {
                    "weixin" : {
                        "appid" : "wx4cfb525c7e90958c",
                        "UniversalLinks" : "https://static-mp-72b82a91-1852-445f-bb5a-a7f5e505366c.next.bspapp.com/uni-universallinks/__UNI__2DF6F7C/"
                    },
                    "qq" : {
                        "appid" : "1112268039",
                        "UniversalLinks" : "https://static-mp-72b82a91-1852-445f-bb5a-a7f5e505366c.next.bspapp.com/qq_conn/1112268039/"
                    }
                },
                "payment" : {
                    "alipay" : {
                        "__platform__" : [ "ios", "android" ]
                    },
                    "appleiap" : {}
                },
                "oauth" : {
                    "univerify" : {}
                },
                "maps" : {
                    "amap" : {
                        "name" : "amapOiUHXB3i",
                        "appkey_ios" : "1b7896dd696793974201ddd5f8a8f3e4",
                        "appkey_android" : "3ef073a9b403699d7124bda4e4f6f1d4"
                    }
                },
                "push" : {
                    "unipush" : {
                        "version" : "2",
                        "offline" : true,
                        "hms" : {},
                        "vivo" : {},
                        "mi" : {},
                        "honor" : {}
                    }
                }
            },
            "icons" : {
                "android" : {
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png"
                    }
                }
            },
            "splashscreen" : {
                "androidStyle" : "default",
                "useOriginalMsgbox" : true,
                "android" : {
                    "xxhdpi" : "static/images/index/bg.png",
                    "xhdpi" : "static/images/index/bg.png",
                    "hdpi" : "static/images/index/bg.png"
                },
                "iosStyle" : "storyboard",
                "ios" : {
                    "storyboard" : "static/CustomStoryboard.zip"
                }
            }
        },
        "nativePlugins" : {
            "DHQ-AlipayAuth" : {
                "__plugin_info__" : {
                    "name" : "【免费】支付宝授权登录，支持Android和iOS，适用uni集成的支付宝支付",
                    "description" : "iOS && Android 原生授权获取auth_code",
                    "platforms" : "Android,iOS",
                    "url" : "https://ext.dcloud.net.cn/plugin?id=13888",
                    "android_package_name" : "com.uni.lightingball",
                    "ios_bundle_id" : "com.lluuxiu.www",
                    "isCloud" : true,
                    "bought" : 1,
                    "pid" : "13888",
                    "parameters" : {}
                }
            },
            "LY-DCLocationAlive" : {
                "amap_appkey_android" : "3ef073a9b403699d7124bda4e4f6f1d4",
                "amap_appkey_ios" : "1b7896dd696793974201ddd5f8a8f3e4",
                "__plugin_info__" : {
                    "name" : "高德定位、持续定位、后台定位、定位保活、坐标转换、两点间距离计算",
                    "description" : "后台定位、持续定位、高德定位、保活。反馈请联系QQ群【】",
                    "platforms" : "Android,iOS",
                    "url" : "https://ext.dcloud.net.cn/plugin?id=12620",
                    "android_package_name" : "com.uni.lightingball",
                    "ios_bundle_id" : "com.lluuxiu.www",
                    "isCloud" : true,
                    "bought" : 1,
                    "pid" : "12620",
                    "parameters" : {
                        "amap_appkey_android" : {
                            "des" : "android平台下高德地图appkey",
                            "key" : "LY-DCLocationAlive:com.amap.api.v2.apikey",
                            "value" : ""
                        },
                        "amap_appkey_ios" : {
                            "des" : "ios平台下高德地图appkey",
                            "key" : "LY-DCLocationAlive:amap:appkey",
                            "value" : ""
                        }
                    }
                }
            }
        }
    },
    /* 快应用特有相关 */
    "quickapp" : {},
    /* 小程序特有相关 */
    "mp-weixin" : {
        "appid" : "",
        "setting" : {
            "urlCheck" : false
        },
        "usingComponents" : true,
        "requiredPrivateInfos" : [
            "getLocation",
            "onLocationChange",
            "startLocationUpdateBackground",
            "chooseAddress",
            "chooseLocation"
        ]
    },
    "mp-alipay" : {
        "usingComponents" : true
    },
    "mp-baidu" : {
        "usingComponents" : true
    },
    "mp-toutiao" : {
        "usingComponents" : true
    },
    "uniStatistics" : {
        "enable" : false
    },
    "vueVersion" : "2",
    "h5" : {
        "sdkConfigs" : {
            "maps" : {
                "amap" : {
                    "key" : "26ab172d25bd6002eb28192f569071a3",
                    "securityJsCode" : "3b28ed2f1dad1324d661bbb1a2e8cea8",
                    "serviceHost" : ""
                }
            }
        }
    },
    "fallbackLocale" : "en",
    "_spaceID" : "mp-72b82a91-1852-445f-bb5a-a7f5e505366c",
    "_qq_spaceID" : "mp-72b82a91-1852-445f-bb5a-a7f5e505366c"
}
/* 5+App特有相关 */

